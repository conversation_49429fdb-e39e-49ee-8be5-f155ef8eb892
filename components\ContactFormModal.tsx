"use client";

import React, { useState, useEffect } from "react";
import { X } from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { submitContactForm, ContactFormData } from "@/api/contact/contact_api";

interface ContactFormModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const ContactFormModal: React.FC<ContactFormModalProps> = ({
  isOpen,
  onClose,
}) => {
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    countryCode: "+91",
    phoneNumber: "",
    service: "",
    message: "",
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState<
    "idle" | "success" | "error"
  >("idle");
  const [messageError, setMessageError] = useState<string | null>(null);

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;

    if (name === "message") {
      const charCount = value.trim().length;
      if (charCount < 10) {
        setMessageError("Message must be at least 10 characters.");
      } else {
        setMessageError(null);
      }
    }

    setFormData({ ...formData, [name]: value });
  };

  const handleSelectChange = (name: string, value: string) => {
    setFormData({ ...formData, [name]: value });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    const charCount = formData.message.trim().length;
    if (charCount < 10) {
      setMessageError("Message must be at least 10 characters.");
      return;
    }

    setIsSubmitting(true);
    setSubmitStatus("idle");

    try {
      const contactData: ContactFormData = {
        name: formData.name,
        email: formData.email,
        countryCode: formData.countryCode,
        phoneNumber: formData.phoneNumber,
        service: formData.service,
        message: formData.message,
      };

      const result = await submitContactForm(contactData);

      if (result.success) {
        setSubmitStatus("success");
        setFormData({
          name: "",
          email: "",
          countryCode: "+91",
          phoneNumber: "",
          service: "",
          message: "",
        });
        // Auto close modal after 3 seconds on success
        setTimeout(() => {
          onClose();
          setSubmitStatus("idle");
        }, 3000);
      } else {
        setSubmitStatus("error");
        console.error(
          "Form submission failed:",
          result.message || result.errors
        );
      }
    } catch (error) {
      setSubmitStatus("error");
      console.error("Error submitting form:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  if (!isOpen) return null;

  return (
    <div
      className="sticky inset-0 bg-black bg-opacity-80 flex items-center justify-center z-50 p-4"
      onClick={handleBackdropClick}
    >
      <div className="bg-white dark:bg-zinc-900 rounded-xl max-w-2xl w-full overflow-y-auto relative">
        {/* Close Button */}
        <button
          onClick={onClose}
          className="absolute top-4 right-4 p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-colors z-10"
        >
          <X size={24} />
        </button>

        {/* Modal Content */}
        <div className="p-8">
          <div className="text-center mb-8">
            <h2 className="text-3xl font-bold text-black dark:text-white mb-2">
              Get In Touch
            </h2>
            <p className="text-gray-600 dark:text-gray-400">
              Let's discuss your project and bring your ideas to life
            </p>
          </div>

          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Name and Email Row */}
            <div className="flex flex-col md:flex-row gap-6">
              <div className="flex-1">
                <label className="block mb-2 text-lg text-black dark:text-white">
                  Name*
                </label>
                <input
                  type="text"
                  name="name"
                  value={formData.name}
                  onChange={handleChange}
                  className="w-full bg-transparent border-b border-gray-400 dark:border-zinc-700 outline-none py-2 text-black dark:text-white placeholder-gray-500 dark:placeholder-gray-400"
                  placeholder="Your Name"
                  required
                />
              </div>

              <div className="flex-1">
                <label className="block mb-2 text-lg text-black dark:text-white">
                  Email*
                </label>
                <input
                  type="email"
                  name="email"
                  value={formData.email}
                  onChange={handleChange}
                  className="w-full bg-transparent border-b border-gray-400 dark:border-zinc-700 outline-none py-2 text-black dark:text-white placeholder-gray-500 dark:placeholder-gray-400"
                  placeholder="<EMAIL>"
                  required
                />
              </div>
            </div>

            {/* Phone and Service Row */}
            <div className="flex flex-col md:flex-row gap-6">
              <div className="flex-1">
                <label className="block mb-2 text-lg text-black dark:text-white">
                  Phone Number
                </label>
                <div className="flex gap-2">
                  <div className="w-32">
                    <Select
                      value={formData.countryCode}
                      onValueChange={(value) =>
                        handleSelectChange("countryCode", value)
                      }
                    >
                      <SelectTrigger className="bg-transparent border-b border-gray-400 dark:border-zinc-700 border-t-0 border-l-0 border-r-0 rounded-none text-black dark:text-white focus:ring-0 focus:ring-offset-0 focus:border-gray-400 dark:focus:border-zinc-700">
                        <SelectValue placeholder="Code" />
                      </SelectTrigger>
                      <SelectContent className="bg-white dark:bg-zinc-800 border-gray-300 dark:border-zinc-700 text-black dark:text-white">
                        <SelectItem value="+1">🇺🇸 +1</SelectItem>
                        <SelectItem value="+91">🇮🇳 +91</SelectItem>
                        <SelectItem value="+44">🇬🇧 +44</SelectItem>
                        <SelectItem value="+86">🇨🇳 +86</SelectItem>
                        <SelectItem value="+81">🇯🇵 +81</SelectItem>
                        <SelectItem value="+49">🇩🇪 +49</SelectItem>
                        <SelectItem value="+33">🇫🇷 +33</SelectItem>
                        <SelectItem value="+39">🇮🇹 +39</SelectItem>
                        <SelectItem value="+34">🇪🇸 +34</SelectItem>
                        <SelectItem value="+7">🇷🇺 +7</SelectItem>
                        <SelectItem value="+55">🇧🇷 +55</SelectItem>
                        <SelectItem value="+61">🇦🇺 +61</SelectItem>
                        <SelectItem value="+82">🇰🇷 +82</SelectItem>
                        <SelectItem value="+52">🇲🇽 +52</SelectItem>
                        <SelectItem value="+31">🇳🇱 +31</SelectItem>
                        <SelectItem value="+46">🇸🇪 +46</SelectItem>
                        <SelectItem value="+47">🇳🇴 +47</SelectItem>
                        <SelectItem value="+45">🇩🇰 +45</SelectItem>
                        <SelectItem value="+41">🇨🇭 +41</SelectItem>
                        <SelectItem value="+43">🇦🇹 +43</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="flex-1">
                    <input
                      type="tel"
                      name="phoneNumber"
                      value={formData.phoneNumber}
                      onChange={handleChange}
                      className="w-full bg-transparent border-b border-gray-400 dark:border-zinc-700 outline-none py-2 text-black dark:text-white placeholder-gray-500 dark:placeholder-gray-400"
                      placeholder="Your phone number"
                    />
                  </div>
                </div>
              </div>

              <div className="flex-1">
                <label className="block mb-2 text-lg text-black dark:text-white">
                  Service
                </label>
                <input
                  type="text"
                  name="service"
                  value={formData.service}
                  onChange={handleChange}
                  className="w-full bg-transparent border-b border-gray-400 dark:border-zinc-700 outline-none py-2 text-black dark:text-white placeholder-gray-500 dark:placeholder-gray-400"
                  placeholder="What service are you interested in?"
                />
              </div>
            </div>

            {/* Message */}
            <div>
              <label className="block mb-2 text-lg text-black dark:text-white">
                Your Message*
              </label>
              <textarea
                name="message"
                value={formData.message}
                onChange={handleChange}
                rows={4}
                className="w-full bg-transparent border-b border-gray-400 dark:border-zinc-700 outline-none py-2 text-black dark:text-white placeholder-gray-500 dark:placeholder-gray-400 resize-none"
                placeholder="Write your message here..."
                required
              />
              {messageError && (
                <div className="text-red-400 text-sm mt-1">{messageError}</div>
              )}
              <div className="text-gray-500 dark:text-gray-400 text-sm mt-1">
                Character count: {formData.message.trim().length}/10
              </div>
            </div>

            {/* Status Messages */}
            {submitStatus === "success" && (
              <div className="text-green-400 text-center py-2 bg-green-50 dark:bg-green-900/20 rounded-lg">
                Message sent successfully! We'll get back to you soon.
              </div>
            )}
            {submitStatus === "error" && (
              <div className="text-red-400 text-center py-2 bg-red-50 dark:bg-red-900/20 rounded-lg">
                Failed to send message. Please try again.
              </div>
            )}

            {/* Submit Button */}
            <button
              type="submit"
              disabled={isSubmitting || !!messageError}
              className="w-full bg-orange-500 hover:bg-orange-600 disabled:bg-orange-400 disabled:cursor-not-allowed transition-colors text-white py-3 rounded-lg font-medium text-lg"
            >
              {isSubmitting ? "SENDING..." : "SEND MESSAGE"}
            </button>
          </form>
        </div>
      </div>
    </div>
  );
};

export default ContactFormModal;
